name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        type: string

env:
  GO_VERSION: '1.24.4'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download

      - name: Run tests
        run: make test

      - name: Run linters
        run: make lint

  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - goos: linux
            goarch: amd64
          - goos: linux
            goarch: arm64
          - goos: darwin
            goarch: amd64
          - goos: darwin
            goarch: arm64
          - goos: windows
            goarch: amd64

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Get version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Build binary
        env:
          GOOS: ${{ matrix.goos }}
          GOARCH: ${{ matrix.goarch }}
          VERSION: ${{ steps.version.outputs.version }}
        run: |
          BINARY_NAME=mkcd
          if [ "$GOOS" = "windows" ]; then
            BINARY_NAME=mkcd.exe
          fi
          
          mkdir -p dist/mkcd-$VERSION-$GOOS-$GOARCH
          
          go build -ldflags="-s -w -X main.Version=$VERSION -X main.Commit=$GITHUB_SHA -X main.BuildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            -o dist/mkcd-$VERSION-$GOOS-$GOARCH/$BINARY_NAME .
          
          # Copy documentation
          cp README.md LICENSE CHANGELOG.md dist/mkcd-$VERSION-$GOOS-$GOARCH/ 2>/dev/null || true

      - name: Create archive
        id: archive
        env:
          GOOS: ${{ matrix.goos }}
          GOARCH: ${{ matrix.goarch }}
          VERSION: ${{ steps.version.outputs.version }}
        run: |
          cd dist
          ARCHIVE_NAME=mkcd-$VERSION-$GOOS-$GOARCH
          
          if [ "$GOOS" = "windows" ]; then
            zip -r $ARCHIVE_NAME.zip $ARCHIVE_NAME/
            echo "archive=$ARCHIVE_NAME.zip" >> $GITHUB_OUTPUT
          else
            tar -czf $ARCHIVE_NAME.tar.gz $ARCHIVE_NAME/
            echo "archive=$ARCHIVE_NAME.tar.gz" >> $GITHUB_OUTPUT
          fi

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: mkcd-${{ steps.version.outputs.version }}-${{ matrix.goos }}-${{ matrix.goarch }}
          path: dist/${{ steps.archive.outputs.archive }}

  release:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Get version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: dist/

      - name: Prepare release assets
        run: |
          mkdir -p release-assets
          find dist/ -name "*.tar.gz" -o -name "*.zip" | xargs -I {} cp {} release-assets/
          
          # Generate checksums
          cd release-assets
          sha256sum * > checksums.txt

      - name: Extract changelog for version
        id: changelog
        run: |
          VERSION=${{ steps.version.outputs.version }}
          # Extract the changelog section for this version
          awk "/^## \[${VERSION#v}\]/{flag=1; next} /^## \[/{flag=0} flag" CHANGELOG.md > release-notes.md
          
          # If no specific version found, use the latest unreleased section
          if [ ! -s release-notes.md ]; then
            awk "/^## \[Unreleased\]/{flag=1; next} /^## \[/{flag=0} flag" CHANGELOG.md > release-notes.md
          fi

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ steps.version.outputs.version }}
          name: Release ${{ steps.version.outputs.version }}
          body_path: release-notes.md
          files: release-assets/*
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  homebrew:
    needs: release
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Get version
        id: version
        run: echo "version=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Generate Homebrew formula
        run: |
          VERSION=${{ steps.version.outputs.version }}
          
          # Download the source archive and calculate its SHA256
          curl -sL "https://github.com/mochajutsu/mkcd/archive/v${VERSION}.tar.gz" -o source.tar.gz
          SHA256=$(sha256sum source.tar.gz | cut -d' ' -f1)
          
          # Generate the formula
          mkdir -p packaging/homebrew
          sed "s/# TODO: Replace with actual SHA256 of v${VERSION}.tar.gz/${SHA256}/" \
            <(./scripts/generate-homebrew-formula.sh $VERSION) > packaging/homebrew/mkcd.rb

      - name: Upload Homebrew formula
        uses: actions/upload-artifact@v4
        with:
          name: homebrew-formula
          path: packaging/homebrew/mkcd.rb
