# Release Guide for mkcd

This document outlines the complete process for releasing mkcd across multiple platforms and package managers.

## Prerequisites

- Go 1.24.4 or later
- Git with proper authentication
- GitHub CLI (optional, for automated releases)
- Access to package manager repositories (for distribution)

## Release Process

### 1. Prepare for Release

```bash
# Ensure you're on the main branch
git checkout main
git pull origin main

# Update version in relevant files if needed
# The version is automatically determined from git tags

# Update CHANGELOG.md with release notes
# Make sure all changes are documented
```

### 2. Create Release Artifacts

```bash
# Clean previous builds
make clean

# Run full release process (tests, lint, build, package)
make release
```

This command will:
- Clean previous build artifacts
- Run all tests with coverage
- Run linters and code quality checks
- Build binaries for all supported platforms
- Create distribution packages (.tar.gz files)
- Generate SHA256 checksums

### 3. Verify Release Artifacts

After running `make release`, verify the contents of the `dist/` directory:

```bash
ls -la dist/
```

You should see:
- `mkcd-<version>-<os>-<arch>.tar.gz` files for each platform
- `checksums.txt` with SHA256 hashes
- Individual platform directories with binaries and documentation

### 4. Create GitHub Release

#### Option A: Manual GitHub Release

1. Go to https://github.com/mochajutsu/mkcd/releases
2. Click "Create a new release"
3. Create a new tag (e.g., `v1.0.0`)
4. Use the release title: "Release v1.0.0"
5. Copy the relevant section from CHANGELOG.md for the release notes
6. Upload all files from the `dist/` directory
7. Publish the release

#### Option B: Automated GitHub Release (with GitHub Actions)

```bash
# Create and push a tag
git tag v1.0.0
git push origin v1.0.0
```

The GitHub Actions workflow will automatically:
- Build and test the code
- Create release artifacts
- Create a GitHub release with the artifacts
- Generate Homebrew formula

### 5. Package Manager Distribution

#### Homebrew (macOS/Linux)

After the GitHub release is created:

```bash
# Generate Homebrew formula
make homebrew

# The formula will be in packaging/homebrew/mkcd.rb
# Submit to homebrew-tap repository or create a tap
```

For a custom tap:
1. Create a repository named `homebrew-mkcd`
2. Add the formula to `Formula/mkcd.rb`
3. Users can install with: `brew tap mochajutsu/mkcd && brew install mkcd`

#### Arch Linux (AUR)

```bash
# Generate PKGBUILD
make arch

# The PKGBUILD will be in packaging/arch/PKGBUILD
# Submit to AUR following their guidelines
```

Steps for AUR submission:
1. Create an AUR account
2. Clone the AUR repository: `git clone ssh://<EMAIL>/mkcd.git`
3. Copy the generated PKGBUILD
4. Update checksums with actual values
5. Commit and push to AUR

#### Ubuntu/Debian (PPA)

For Ubuntu PPA distribution:
1. Create a Debian package structure
2. Use tools like `dh_make` or `debuild`
3. Submit to a Personal Package Archive (PPA)

#### Docker

The project includes a Dockerfile and GoReleaser configuration for Docker images:

```bash
# Build Docker image
make docker

# Or use GoReleaser for multi-platform images
goreleaser release --snapshot --clean
```

## GoReleaser Integration

The project includes a comprehensive `.goreleaser.yml` configuration that automates:

- Cross-platform builds
- Archive creation
- Checksum generation
- GitHub releases
- Homebrew tap updates
- Docker image builds
- AUR package generation
- Snapcraft packages

To use GoReleaser:

```bash
# Install GoReleaser
go install github.com/goreleaser/goreleaser@latest

# Create a release
goreleaser release --clean

# Create a snapshot (without publishing)
goreleaser release --snapshot --clean
```

## GitHub Actions Workflows

The project includes two main workflows:

### CI Workflow (`.github/workflows/ci.yml`)
- Runs on every push and pull request
- Tests across multiple Go versions and platforms
- Runs security scans and linters
- Builds binaries for verification

### Release Workflow (`.github/workflows/release.yml`)
- Triggers on tag pushes (v*)
- Builds release artifacts
- Creates GitHub releases
- Generates package manager files

## Version Management

### Semantic Versioning

The project follows [Semantic Versioning](https://semver.org/):
- `MAJOR.MINOR.PATCH` (e.g., 1.0.0)
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes (backward compatible)

### Tagging Strategy

```bash
# For a new major release
git tag v2.0.0

# For a new minor release
git tag v1.1.0

# For a patch release
git tag v1.0.1

# For pre-releases
git tag v1.1.0-beta.1
git tag v1.1.0-rc.1
```

## Release Checklist

- [ ] Update CHANGELOG.md with new version
- [ ] Ensure all tests pass (`make test`)
- [ ] Run linters (`make lint`)
- [ ] Build and test locally (`make build`)
- [ ] Create and push git tag
- [ ] Verify GitHub Actions workflow completes
- [ ] Test installation from release artifacts
- [ ] Update package managers (Homebrew, AUR, etc.)
- [ ] Announce release (social media, forums, etc.)

## Troubleshooting

### Build Issues

```bash
# Clean everything and rebuild
make clean
go clean -modcache
make deps
make build
```

### Missing Dependencies

```bash
# Ensure all dependencies are available
make check-deps

# Update dependencies
make deps-update
```

### Cross-compilation Issues

```bash
# Test specific platform builds
GOOS=linux GOARCH=amd64 go build .
GOOS=darwin GOARCH=arm64 go build .
GOOS=windows GOARCH=amd64 go build .
```

## Post-Release Tasks

1. **Update Documentation**: Ensure README.md and docs reflect new features
2. **Update Examples**: Update usage examples if new features were added
3. **Social Media**: Announce the release on relevant platforms
4. **Package Managers**: Monitor package manager submissions
5. **Issue Tracking**: Close resolved issues and update project boards

## Security Considerations

- All release artifacts are signed with checksums
- GitHub Actions uses secure token handling
- Dependencies are regularly updated and scanned
- Release process includes security scanning

For security issues, please follow the security policy in the main repository.
